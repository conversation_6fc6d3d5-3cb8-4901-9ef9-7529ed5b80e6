# GitHub Personal Access Token Configuration
# Copy this file to .env and fill in your actual values

# Your GitHub username
GITHUB_USERNAME=your_github_username

# Your GitHub Personal Access Token (PAT)
# This token needs the following permissions for READ-ONLY access:
# For private repositories: repo (Full control of private repositories) OR contents:read + metadata:read
# For organization access: read:org (Read org and team membership)
#
# Note: GitHub's "repo" scope is unfortunately broad, but it's the only way to access private repos.
# The tool only performs read operations (clone, fetch, read commits/tags).
GITHUB_PAT=your_personal_access_token_here

# How to create a GitHub Personal Access Token:
# 1. Go to GitHub Settings > Developer settings > Personal access tokens > Tokens (classic)
# 2. Click "Generate new token (classic)"
# 3. Give it a descriptive name like "V1 Commit Log Flutter Tool - Read Only"
# 4. Select the following scopes:
#    - repo (needed for private repository access - tool only reads, never writes)
#    - read:org (read organization membership)
# 5. Click "Generate token"
# 6. Copy the token and paste it as the value for GITHUB_PAT above
# 7. Save this file as .env (without the .example extension)

# Note: Keep your PAT secure and never commit it to version control!
