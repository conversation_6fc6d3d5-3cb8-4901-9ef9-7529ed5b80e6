# V1 Commit Log Flutter

This script scans the pubspec in a Flutter project to get all changes for its dependencies based on a given release tag range and compiles the list of commits in a commit log.

## Prerequisites

- Python 3.11+
- GitHub Personal Access Token (PAT) with appropriate permissions

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure GitHub Authentication

This tool uses GitHub Personal Access Tokens (PAT) for authentication instead of SSH keys.

1. **Create a GitHub Personal Access Token:**
   - Go to GitHub Settings > Developer settings > Personal access tokens > Tokens (classic)
   - Click "Generate new token (classic)"
   - Give it a descriptive name like "V1 Commit Log Flutter Tool"
   - Select the following scopes:
     - `repo` (Full control of private repositories)
     - `read:org` (Read org and team membership, read org projects)
   - Click "Generate token"
   - **Copy the token immediately** (you won't be able to see it again)

2. **Set up environment variables:**
   - Copy `.env.example` to `.env`
   - Fill in your GitHub username and PAT:
     ```
     GITHUB_USERNAME=your_github_username
     GITHUB_PAT=your_personal_access_token_here
     ```

⚠️ **Important:** Never commit your `.env` file to version control. It's already included in `.gitignore`.

## Usage

This is the flow:

![image](https://github.com/p01pienaara/commit-message-history/assets/80837554/40704f19-4d3f-4ba7-bae6-06a3f3022aa8)

### Commands

For specific version diff:
```bash
python main.py --old <old_version> --new <new_version>
```

For latest market release diff:
```bash
python main.py --repo <repo_url>
```

For tenant-specific releases:
```bash
python main.py --tenant <tenant_number>
```

## Output

The tool generates:
- `logs/commit_log.txt` - Latest commit log
- `logs/commit_log_<old_version>-<new_version>.txt` - Version-specific commit log
- `dependencies.json` - Structured dependency change data
