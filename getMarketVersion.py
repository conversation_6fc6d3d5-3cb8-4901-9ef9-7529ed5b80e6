import subprocess
import os
from git import Repo
import re
import json
import manageRepo

def format_string_to_dict(s):
    # Split the string by '-' to separate version and the rest
    version, rest = s.split('-')
    
    # Split the rest by '+' to separate environment and iteration
    environment, iteration = rest.split('+')
    
    # Form the dictionary
    result = {
        "tag": s,
        "version": version,
        "environment": environment,
        "iteration": iteration
    }
    
    return result

def get_latest_releases(repo_url, num_releases=2):
    """Gets a specified number of latest release tags from a Git repository.

    Args:
        repo_url: The URL of the Git repository.
        num_releases: The number of release tags to retrieve.

    Returns:
        A list of release tags (e.g., ["v2.1.0", "v2.0.1"]).
    """

    manageRepo.update_repo(repo_url)
    dir_name = manageRepo.get_repo_directory_name(repo_url)
    repo = Repo(f".temp_repo/{dir_name}")
    tags = sorted(repo.tags, key=lambda t: t.commit.committed_datetime, reverse=True)
    repo.close()
    return [tag.name for tag in tags[:num_releases]]

def parseReleseVersions(repo_url):
    releases = get_latest_releases(repo_url)
    formatted_releases = []
    for release in releases:
        formatted_releases.append(format_string_to_dict(release))

    return formatted_releases
    