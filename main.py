


import argparse
import getcommits
import getUpdatedDependencies
import getMarketVersion
import timer
import url_utils

def main(old_version=None, new_version=None, url=None, tenant=None):
    start_time = timer.get_current_time_and_date()
    if old_version is not None and new_version is not None:
        getUpdatedDependencies.updateWithRange(url_utils.CENTRAL_APP_REPO, range=[new_version,old_version])
        getcommits.fetch()
    elif(url is not None):
        release_versions = getMarketVersion.parseReleseVersions(url)
        getUpdatedDependencies.updateWithRange(url_utils.CENTRAL_APP_REPO, range=[release_versions[0]['version'],release_versions[1]['version']])
        getcommits.fetch()
    elif(tenant is not None):
        tenant_repo_url = url_utils.get_tenant_repo_url(tenant)
        release_versions = getMarketVersion.parseReleseVersions(tenant_repo_url)
        if release_versions[0]['version'] != release_versions[1]['version']:
            getUpdatedDependencies.updateWithRange(url_utils.CENTRAL_APP_REPO, range=[release_versions[0]['version'],release_versions[1]['version']])
            getcommits.fetch()
        else:
            print(f"Incremental build on {release_versions[0]['version']}, no changes to core codebase")

    end_time = timer.get_current_time_and_date()
    total_time = timer.get_time_difference(start_time, end_time)
    print("finished in " + total_time)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="This script combines the commits messages from all changed dependencies in a modular Flutter app to easily see what changed from version to version")
    parser.add_argument("--old", type=str, help="Provide the old and new version together to specify a version range to pull")
    parser.add_argument("--new", type=str, help="Provide the old and new version together to specify a version range to pull")
    parser.add_argument("--repo", type=str, help="Provide the market repo url to get the changes for the latest 2 releases")
    parser.add_argument("--tenant", type=str, help="Provide the market tenant number to get the changes for the latest 2 releases")

    args = parser.parse_args()

    main(old_version=args.old, new_version=args.new, url=args.repo, tenant=args.tenant)
    