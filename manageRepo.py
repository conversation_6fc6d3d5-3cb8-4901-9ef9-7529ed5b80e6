import os
import re
from dotenv import load_dotenv
from git import Repo, GitCommandError

load_dotenv()
pat = os.getenv('GITHUB_PAT')
username = os.getenv('GITHUB_USERNAME')

def validate_environment():
    """
    Validates that required environment variables are set.
    Provides helpful error messages if they're missing.
    """
    if not pat:
        raise ValueError(
            "GITHUB_PAT environment variable is not set.\n"
            "Please create a .env file with your GitHub Personal Access Token.\n"
            "See .env.example for instructions."
        )

    if not username:
        raise ValueError(
            "GITHUB_USERNAME environment variable is not set.\n"
            "Please add your GitHub username to the .env file.\n"
            "See .env.example for instructions."
        )

def convert_ssh_to_https(repo_url):
    """
    Converts SSH Git URL to HTTPS URL for PAT authentication.

    :param repo_url: SSH or HTTPS Git URL
    :return: HTTPS Git URL with PAT authentication
    """
    validate_environment()

    # If it's already an HTTPS URL, return as is (but ensure it has PAT auth)
    if repo_url.startswith('https://'):
        # Check if it already has authentication
        if '@' in repo_url:
            return repo_url
        # Add PAT authentication
        return repo_url.replace('https://github.com/', f'https://{username}:{pat}@github.com/')

    # Convert SSH URL to HTTPS with PAT
    if repo_url.startswith('**************:'):
        # Extract owner/repo from SSH URL
        match = re.match(r'git@github\.com:(.+)\.git$', repo_url)
        if match:
            owner_repo = match.group(1)
            return f'https://{username}:{pat}@github.com/{owner_repo}.git'

    # If it's not a recognized format, return as is
    return repo_url

def get_repo_directory_name(repo_url):
    """
    Generates a safe directory name from the repository URL.

    :param repo_url: Git repository URL
    :return: Safe directory name
    """
    # Remove protocol and authentication info
    clean_url = repo_url
    if clean_url.startswith('https://'):
        clean_url = re.sub(r'https://[^@]*@', '', clean_url)
        clean_url = clean_url.replace('https://github.com/', '')
    elif clean_url.startswith('**************:'):
        clean_url = clean_url.replace('**************:', '')

    # Remove .git suffix if present
    if clean_url.endswith('.git'):
        clean_url = clean_url[:-4]

    # Replace slashes with underscores for directory name
    return clean_url.replace('/', '_')

def update_repo(repo_url):
    """
    Clones the repository if not already cloned, ensures the working tree is clean by discarding any changes,
    and pulls the latest changes if already cloned. Uses PAT authentication for HTTPS access.

    :param repo_url: URL of the repository to clone/pull (SSH or HTTPS)
    """
    # Convert SSH URL to HTTPS with PAT authentication
    https_url = convert_ssh_to_https(repo_url)

    # Generate a safe directory name
    dir_name = get_repo_directory_name(repo_url)
    temp_dir = f".temp_repo/{dir_name}"

    try:
        # Check if the repo directory exists
        if not os.path.exists(temp_dir):
            print(f"Cloning repository from {repo_url} to {temp_dir}")
            # Create temp_repo directory if it doesn't exist
            os.makedirs(".temp_repo", exist_ok=True)
            Repo.clone_from(https_url, temp_dir)
        else:
            print(f"Repository already exists at {temp_dir}. Checking status...")

            # Open the existing repository
            repo = Repo(temp_dir)

            # Check if the working tree is clean
            if repo.is_dirty(untracked_files=True):
                print("Working tree is not clean. Resetting changes...")
                repo.git.reset('--hard')
                repo.git.clean('-fd')

            # Update remote URL to use PAT authentication
            origin = repo.remotes.origin
            origin.set_url(https_url)

            # Pull the latest changes from the remote repository
            print("Pulling the latest changes...")
            origin.pull()

    except GitCommandError as e:
        print(f"An error occurred while updating the repository: {e}")
        if "authentication failed" in str(e).lower():
            print("Authentication failed. Please check your GITHUB_PAT and GITHUB_USERNAME environment variables.")
        raise

