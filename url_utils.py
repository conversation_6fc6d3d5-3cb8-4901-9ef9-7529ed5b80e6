"""
Utility functions for handling Git repository URLs and converting between SSH and HTTPS formats.
"""

def convert_ssh_to_https_url(ssh_url):
    """
    Converts SSH Git URL to HTTPS URL (without authentication).
    This is used for converting hardcoded SSH URLs to HTTPS format.
    
    :param ssh_url: SSH Git URL (e.g., **************:owner/repo.git)
    :return: HTTPS Git URL (e.g., https://github.com/owner/repo.git)
    """
    if ssh_url.startswith('https://'):
        return ssh_url
    
    if ssh_url.startswith('**************:'):
        # Extract owner/repo from SSH URL
        repo_part = ssh_url.replace('**************:', '')
        return f'https://github.com/{repo_part}'
    
    return ssh_url

# Constants for commonly used repository URLs (converted to HTTPS)
CENTRAL_APP_REPO = "https://github.com/discovery-ltd/v1-gutenberg-central-app-flutter.git"

def get_tenant_repo_url(tenant):
    """
    Generate tenant repository URL in HTTPS format.
    
    :param tenant: Tenant identifier
    :return: HTTPS URL for the tenant repository
    """
    return f"https://github.com/discovery-ltd/v1-tenant-{tenant}-flutter.git"
