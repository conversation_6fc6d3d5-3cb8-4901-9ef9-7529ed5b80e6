#!/usr/bin/env python3
"""
Setup validation script for V1 Commit Log Flutter tool.
Run this script to verify your GitHub PAT configuration is working correctly.
"""

import os
import sys
from dotenv import load_dotenv
import manageRepo
import url_utils

def main():
    print("🔍 Validating V1 Commit Log Flutter setup...")
    print()
    
    # Load environment variables
    load_dotenv()
    
    try:
        # Test environment variables
        print("1. Checking environment variables...")
        manageRepo.validate_environment()
        print("   ✅ GITHUB_USERNAME and GITHUB_PAT are set")
        
        # Test URL conversion
        print("\n2. Testing URL conversion...")
        test_ssh_url = "**************:discovery-ltd/v1-gutenberg-central-app-flutter.git"
        test_https_url = url_utils.convert_ssh_to_https_url(test_ssh_url)
        print(f"   SSH URL: {test_ssh_url}")
        print(f"   HTTPS URL: {test_https_url}")
        print("   ✅ URL conversion working")
        
        # Test directory name generation
        print("\n3. Testing directory name generation...")
        dir_name = manageRepo.get_repo_directory_name(test_ssh_url)
        print(f"   Directory name: {dir_name}")
        print("   ✅ Directory name generation working")
        
        # Test tenant URL generation
        print("\n4. Testing tenant URL generation...")
        tenant_url = url_utils.get_tenant_repo_url("123")
        print(f"   Tenant URL: {tenant_url}")
        print("   ✅ Tenant URL generation working")
        
        print("\n🎉 All tests passed! Your setup is ready to use.")
        print("\nYou can now run the main script with commands like:")
        print("  python3 main.py --old 3.0.1787 --new 3.0.1835")
        print("  python3 main.py --tenant 123")
        
    except ValueError as e:
        print(f"   ❌ Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
